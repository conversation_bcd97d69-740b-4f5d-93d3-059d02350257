// renderer.js
document.addEventListener('DOMContentLoaded', () => {
    const schedulesList = document.getElementById('schedules-list');
    const addBtn = document.getElementById('add-schedule');
    const saveBtn = document.getElementById('save-schedules');

    // 添加一个新的时间设置行
    const addScheduleRow = (timeValue = '', selectedDays = []) => {
        const itemId = `schedule-${Date.now()}`;
        const item = document.createElement('div');
        item.classList.add('schedule-item');
        item.id = itemId;
        item.innerHTML = `
            <input type="time" class="time" step="1" value="${timeValue}">
            <span class="days">
                星期:
                <input type="checkbox" value="1" ${selectedDays.includes(1) ? 'checked' : ''}>一
                <input type="checkbox" value="2" ${selectedDays.includes(2) ? 'checked' : ''}>二
                <input type="checkbox" value="3" ${selectedDays.includes(3) ? 'checked' : ''}>三
                <input type="checkbox" value="4" ${selectedDays.includes(4) ? 'checked' : ''}>四
                <input type="checkbox" value="5" ${selectedDays.includes(5) ? 'checked' : ''}>五
                <input type="checkbox" value="6" ${selectedDays.includes(6) ? 'checked' : ''}>六
                <input type="checkbox" value="7" ${selectedDays.includes(7) ? 'checked' : ''}>日
            </span>
            <button onclick="document.getElementById('${itemId}').remove()">删除</button>
        `;
        schedulesList.appendChild(item);
    };

    // 保存设置并发送到主进程
    const saveSchedules = () => {
        const schedules = [];
        const items = document.querySelectorAll('.schedule-item');
        items.forEach(item => {
            const timeInput = item.querySelector('.time').value;
            if (!timeInput) return; // 如果未设置时间则跳过

            const selectedDays = [];
            item.querySelectorAll('.days input:checked').forEach(day => {
                selectedDays.push(parseInt(day.value));
            });

            if (selectedDays.length > 0) {
                schedules.push({
                    time: timeInput.replace(/:/g, '-'), // 格式化为 hh-mm-ss
                    days: selectedDays
                });
            }
        });
        window.electronAPI.setShutdownSchedules(schedules);
        alert('计划已保存！程序将在后台监听。');
    };

    // 加载已保存的配置
    const loadSavedSchedules = async () => {
        try {
            const savedSchedules = await window.electronAPI.getShutdownSchedules();
            if (savedSchedules && savedSchedules.length > 0) {
                // 清空现有的计划列表
                schedulesList.innerHTML = '';

                // 加载保存的计划
                savedSchedules.forEach(schedule => {
                    // 将时间格式从 hh-mm-ss 转换为 hh:mm:ss
                    const timeValue = schedule.time.replace(/-/g, ':');
                    addScheduleRow(timeValue, schedule.days);
                });

                console.log('已加载保存的配置:', savedSchedules);
            } else {
                // 如果没有保存的配置，添加一个空行
                addScheduleRow();
            }
        } catch (error) {
            console.error('加载配置失败:', error);
            // 出错时添加一个空行
            addScheduleRow();
        }
    };

    addBtn.addEventListener('click', () => addScheduleRow());
    saveBtn.addEventListener('click', saveSchedules);

    // 加载已保存的配置
    loadSavedSchedules();
});