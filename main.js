// main.js
const { app, BrowserWindow, ipc<PERSON>ain, Tray, <PERSON>u, screen } = require('electron');
const path = require('path');
const { exec } = require('child_process');
const fs = require('fs');

let tray = null;
let mainWindow = null;
let countdownWindow = null;
let shutdownSchedules = []; // 存储关机计划
let timer = null;

// 配置文件路径
const configPath = path.join(__dirname, 'config.json');

// 保存配置到文件
const saveConfig = () => {
  try {
    const config = {
      shutdownSchedules: shutdownSchedules,
      lastUpdated: new Date().toISOString()
    };
    fs.writeFileSync(configPath, JSON.stringify(config, null, 2), 'utf8');
    console.log('配置已保存到:', configPath);
  } catch (error) {
    console.error('保存配置失败:', error);
  }
};

// 从文件加载配置
const loadConfig = () => {
  try {
    if (fs.existsSync(configPath)) {
      const configData = fs.readFileSync(configPath, 'utf8');
      const config = JSON.parse(configData);
      shutdownSchedules = config.shutdownSchedules || [];
      console.log('配置已加载:', shutdownSchedules);
      console.log('上次更新时间:', config.lastUpdated);

      // 如果有计划，启动定时器
      if (shutdownSchedules.length > 0) {
        timer = setInterval(checkShutdownCondition, 1000);
        console.log('定时器已启动');
      }
    } else {
      console.log('配置文件不存在，使用默认配置');
    }
  } catch (error) {
    console.error('加载配置失败:', error);
    shutdownSchedules = [];
  }
};

// 创建主窗口
const createMainWindow = () => {
  mainWindow = new BrowserWindow({
    width: 800,
    height: 600,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      contextIsolation: true,
      nodeIntegration: false,
    },
  });
  mainWindow.loadFile('index.html');
  mainWindow.on('closed', () => {
    mainWindow = null;
  });
};

// 创建系统托盘
const createTray = () => {
  tray = new Tray(path.join(__dirname, 'icon.png')); // 请准备一个 icon.png
  const contextMenu = Menu.buildFromTemplate([
    { label: '显示/隐藏', click: () => mainWindow.isVisible() ? mainWindow.hide() : mainWindow.show() },
    { label: '退出', click: () => app.quit() },
  ]);
  tray.setToolTip('自动关机程序');
  tray.setContextMenu(contextMenu);
};

// 检查是否满足关机条件
const checkShutdownCondition = () => {
  try {
    const now = new Date();
    const dayOfWeek = now.getDay() === 0 ? 7 : now.getDay(); // 周日为7
    const currentTime = `${now.getHours().toString().padStart(2, '0')}-${now.getMinutes().toString().padStart(2, '0')}-${now.getSeconds().toString().padStart(2, '0')}`;

    // 调试信息（每分钟输出一次，避免日志过多）
    if (now.getSeconds() === 0) {
      console.log(`当前时间: ${currentTime}, 星期: ${dayOfWeek}, 计划数量: ${shutdownSchedules.length}`);
    }

    for (const schedule of shutdownSchedules) {
      if (schedule.days.includes(dayOfWeek) && schedule.time === currentTime) {
        console.log('触发关机计划:', schedule);
        createCountdownWindow();
        if(mainWindow) mainWindow.hide(); // 触发关机时隐藏主窗口
        clearInterval(timer); // 找到一个匹配项后停止计时器
        timer = null;
        break;
      }
    }
  } catch (error) {
    console.error('检查关机条件时出错:', error);
  }
};

// 创建全屏倒计时窗口
const createCountdownWindow = () => {
    const { width, height } = screen.getPrimaryDisplay().workAreaSize;
    countdownWindow = new BrowserWindow({
        width,
        height,
        fullscreen: true,
        frame: false,
        alwaysOnTop: true,
        webPreferences: {
            preload: path.join(__dirname, 'preload.js'),
            contextIsolation: true,
        }
    });
    countdownWindow.loadFile('countdown.html');
    countdownWindow.on('closed', () => {
        countdownWindow = null;
        // 倒计时结束后，如果没有新的计划，则重新开始检查
        if (shutdownSchedules.length > 0 && !timer) {
            timer = setInterval(checkShutdownCondition, 1000);
        }
    });
};


app.whenReady().then(() => {
  // 启动时加载配置
  loadConfig();

  createTray();
  createMainWindow();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createMainWindow();
    }
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    // app.quit(); // 注释掉此行，即使关闭窗口也保持后台运行
  }
});

// === IPC 通信 ===

// 获取当前关机计划
ipcMain.handle('get-shutdown-schedules', () => {
  return shutdownSchedules;
});

// 更新关机时间表
ipcMain.on('set-shutdown-schedules', (event, schedules) => {
  shutdownSchedules = schedules;
  console.log('更新计划:', shutdownSchedules);

  // 保存配置到文件
  saveConfig();

  if (!timer) {
    // 只有在计时器未运行时才启动
    timer = setInterval(checkShutdownCondition, 1000);
  }
});

// 执行关机命令
ipcMain.on('execute-shutdown', () => {
  // 注意：不同系统的关机命令不同
  // Windows: shutdown /s /t 0
  // macOS/Linux: shutdown -h now
  const command = process.platform === 'win32' ? 'shutdown /s /t 0' : 'shutdown -h now';
  exec(command, (error, stdout, stderr) => {
    if (error) {
      console.error(`执行错误: ${error}`);
      return;
    }
    console.log(`stdout: ${stdout}`);
    console.error(`stderr: ${stderr}`);
  });
});

// 关闭倒计时窗口
ipcMain.on('close-countdown-window', () => {
    if (countdownWindow) {
        countdownWindow.close();
    }
});